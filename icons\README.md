# PWA 图标文件

这个目录包含PWA应用所需的各种尺寸图标文件。

## 图标尺寸说明

- `icon-72x72.png` - 小尺寸图标，用于通知和小部件
- `icon-96x96.png` - 中等尺寸图标，用于快捷方式
- `icon-128x128.png` - 标准尺寸图标
- `icon-144x144.png` - 高分辨率小图标
- `icon-152x152.png` - iOS Safari 图标
- `icon-192x192.png` - 标准PWA图标
- `icon-384x384.png` - 高分辨率图标
- `icon-512x512.png` - 最高分辨率图标，用于启动画面

## 生成图标

1. 打开 `generate-icons.html` 文件
2. 点击"生成所有图标"按钮
3. 下载生成的PNG文件到此目录

或者使用 `create-basic-icons.html` 快速生成基本图标。

## 图标设计要求

- 使用应用主题色 #165DFF
- 包含播放按钮元素，体现视频导航功能
- 支持 maskable 格式，适配各种设备
- 圆角矩形背景，符合现代设计规范
