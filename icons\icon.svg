<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#165DFF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0E4FE1;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f0f9ff;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆角矩形 -->
  <rect width="512" height="512" rx="80" ry="80" fill="url(#bgGradient)"/>
  
  <!-- 播放按钮外圈 -->
  <circle cx="256" cy="256" r="120" fill="none" stroke="url(#iconGradient)" stroke-width="8" opacity="0.3"/>
  
  <!-- 播放按钮 -->
  <circle cx="256" cy="256" r="90" fill="url(#iconGradient)"/>
  
  <!-- 播放三角形 -->
  <polygon points="230,220 230,292 310,256" fill="#165DFF"/>
  
  <!-- 装饰性元素 - 信号波纹 -->
  <g opacity="0.6">
    <!-- 第一层波纹 -->
    <path d="M 180 180 Q 256 120 332 180" fill="none" stroke="url(#iconGradient)" stroke-width="4" stroke-linecap="round"/>
    <path d="M 180 332 Q 256 392 332 332" fill="none" stroke="url(#iconGradient)" stroke-width="4" stroke-linecap="round"/>
    
    <!-- 第二层波纹 -->
    <path d="M 150 150 Q 256 80 362 150" fill="none" stroke="url(#iconGradient)" stroke-width="3" stroke-linecap="round" opacity="0.7"/>
    <path d="M 150 362 Q 256 432 362 362" fill="none" stroke="url(#iconGradient)" stroke-width="3" stroke-linecap="round" opacity="0.7"/>
  </g>
  
  <!-- 小装饰点 -->
  <circle cx="140" cy="140" r="6" fill="url(#iconGradient)" opacity="0.8"/>
  <circle cx="372" cy="140" r="6" fill="url(#iconGradient)" opacity="0.8"/>
  <circle cx="140" cy="372" r="6" fill="url(#iconGradient)" opacity="0.8"/>
  <circle cx="372" cy="372" r="6" fill="url(#iconGradient)" opacity="0.8"/>
</svg>
