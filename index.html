<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>免费视频资源</title>

    <!-- PWA Meta Tags -->
    <meta name="description" content="免费流媒体资源站点导航，提供多条访问线路和实时状态检测">
    <meta name="keywords" content="免费视频,流媒体,在线观看,视频导航,影视资源">
    <meta name="author" content="免费视频资源导航">
    <meta name="theme-color" content="#165DFF">
    <meta name="msapplication-TileColor" content="#165DFF">
    <meta name="msapplication-config" content="/browserconfig.xml">

    <!-- iOS Safari Meta Tags -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="视频导航">
    <link rel="apple-touch-icon" href="/icons/icon-152x152.png">
    <link rel="apple-touch-icon" sizes="72x72" href="/icons/icon-72x72.png">
    <link rel="apple-touch-icon" sizes="96x96" href="/icons/icon-96x96.png">
    <link rel="apple-touch-icon" sizes="128x128" href="/icons/icon-128x128.png">
    <link rel="apple-touch-icon" sizes="144x144" href="/icons/icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/icons/icon-152x152.png">
    <link rel="apple-touch-icon" sizes="192x192" href="/icons/icon-192x192.png">
    <link rel="apple-touch-icon" sizes="384x384" href="/icons/icon-384x384.png">
    <link rel="apple-touch-icon" sizes="512x512" href="/icons/icon-512x512.png">

    <!-- Standard Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="/icons/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/icons/icon-16x16.png">
    <link rel="shortcut icon" href="/favicon.ico">

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">

    <!-- External Resources -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">

    <!-- 配置Tailwind自定义颜色和字体 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#165DFF',
                        success: '#10B981',
                        warning: '#F59E0B',
                        danger: '#EF4444',
                        dark: '#1E293B',
                        light: '#F8FAFC'
                    },
                    fontFamily: {
                        inter: ['Inter', 'system-ui', 'sans-serif'],
                    },
                },
            }
        }
    </script>

    <style type="text/tailwindcss">
        @layer utilities {
      .card-shadow {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      }
      .card-hover {
        transition: all 0.3s ease;
      }
      .card-hover:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
      }
      .status-pulse {
        animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
      }
      @keyframes pulse {
        0%, 100% {
          opacity: 1;
        }
        50% {
          opacity: 0.7;
        }
      }
      .link-item {
        cursor: pointer;
        user-select: none;
      }
      .link-item:hover {
        transform: translateY(-1px);
      }
      .link-item:active {
        transform: translateY(0);
      }

      /* PWA 加载动画 */
      .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #165DFF 0%, #0E4FE1 100%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        transition: opacity 0.5s ease, visibility 0.5s ease;
      }

      .loading-overlay.hidden {
        opacity: 0;
        visibility: hidden;
      }

      .loading-spinner {
        width: 60px;
        height: 60px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 20px;
      }

      .loading-text {
        color: white;
        font-size: 18px;
        font-weight: 500;
        text-align: center;
        margin-bottom: 10px;
      }

      .loading-subtext {
        color: rgba(255, 255, 255, 0.8);
        font-size: 14px;
        text-align: center;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      /* 页面淡入动画 */
      .page-content {
        opacity: 0;
        transform: translateY(20px);
        transition: opacity 0.6s ease, transform 0.6s ease;
      }

      .page-content.loaded {
        opacity: 1;
        transform: translateY(0);
      }

      /* 卡片进入动画 */
      .site-card {
        opacity: 0;
        transform: translateY(30px);
        transition: opacity 0.5s ease, transform 0.5s ease;
      }

      .site-card.animate-in {
        opacity: 1;
        transform: translateY(0);
      }

      /* 脉冲加载效果 */
      .skeleton {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
      }

      @keyframes loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
      }
    }
  </style>
</head>

<body class="bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen font-inter text-dark">
    <!-- PWA 加载覆盖层 -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">免费视频资源导航</div>
        <div class="loading-subtext">正在加载应用...</div>
    </div>

    <!-- 主要内容容器 -->
    <div id="page-content" class="page-content">
    <!-- 顶部导航 -->
    <header class="bg-white shadow-md sticky top-0 z-50 transition-all duration-300">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <i class="fa fa-play-circle text-primary text-2xl"></i>
                <h1 class="text-[clamp(1.25rem,3vw,1.75rem)] font-bold text-primary">免费资源导航</h1>
            </div>
            <div class="flex items-center space-x-4">
                <span id="last-checked" class="text-sm text-gray-500 hidden md:inline-block">
                    <i class="fa fa-refresh mr-1"></i>
                    <span>最后检查: 从未</span>
                </span>
                <button id="refresh-btn"
                    class="bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-lg flex items-center transition-all">
                    <i class="fa fa-refresh mr-2"></i>
                    <span>刷新状态</span>
                </button>
            </div>
        </div>
    </header>

    <!-- 主内容区 -->
    <main class="container mx-auto px-4 py-8">
        <!-- 介绍部分 -->
        <div class="text-center mb-10 max-w-3xl mx-auto">
            <h2 class="text-[clamp(1.5rem,3vw,2rem)] font-bold mb-4">流媒体资源访问</h2>
            <p class="text-gray-600 text-lg">以下是可用的流媒体资源站点，每个站点提供多条访问线路。绿色标识表示线路可正常访问，红色表示线路暂时不可用。</p>
        </div>

        <!-- 状态统计面板 -->
        <div id="status-panel" class="bg-white rounded-xl shadow-lg p-6 mb-8 max-w-4xl mx-auto">
            <h3 class="text-lg font-bold mb-4 flex items-center">
                <i class="fa fa-bar-chart text-primary mr-2"></i>线路状态统计
            </h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-2xl font-bold text-gray-700" id="total-links">-</div>
                    <div class="text-sm text-gray-500">总线路数</div>
                </div>
                <div class="text-center p-4 bg-success/10 rounded-lg">
                    <div class="text-2xl font-bold text-success" id="online-links">-</div>
                    <div class="text-sm text-success">在线线路</div>
                </div>
                <div class="text-center p-4 bg-danger/10 rounded-lg">
                    <div class="text-2xl font-bold text-danger" id="offline-links">-</div>
                    <div class="text-sm text-danger">离线线路</div>
                </div>
                <div class="text-center p-4 bg-primary/10 rounded-lg">
                    <div class="text-2xl font-bold text-primary" id="success-rate">-</div>
                    <div class="text-sm text-primary">成功率</div>
                </div>
            </div>
        </div>

        <!-- 站点卡片容器 -->
        <div id="sites-container" class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            <!-- 站点将通过JavaScript动态生成 -->
        </div>

        <!-- 使用说明 -->
        <div class="bg-white rounded-xl shadow-md p-6 max-w-3xl mx-auto">
            <h3 class="text-xl font-bold mb-4 flex items-center">
                <i class="fa fa-info-circle text-primary mr-2"></i>使用说明
            </h3>
            <ul class="space-y-3 text-gray-600">
                <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-3"></i>
                    <span>点击线路链接将在新窗口打开对应网站</span>
                </li>
                <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-3"></i>
                    <span>如果某条线路无法访问，请尝试其他线路</span>
                </li>
                <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-3"></i>
                    <span>点击"刷新状态"按钮可以重新检查所有线路的可用性</span>
                </li>
            </ul>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="mt-16 py-8">
        <div class="container mx-auto px-4 text-center">
            <p class="mb-4 text-gray-600">免费视频资源</p>
            <p class="text-gray-500 text-sm">© 2025 资源导航页面 | 所有链接均搜集自互联网</p>
        </div>
    </footer>

    <!-- 通知提示容器 -->
    <div id="notification-container" class="fixed bottom-6 right-6 space-y-3 pointer-events-none" style="z-index: 9999;">
        <!-- 通知将动态添加到这里 -->
    </div>

    </div> <!-- 关闭 page-content -->

    <script>
        // 站点数据 - 所有站点信息都集中在此数组中管理
        const sites = [
            {
                name: "LibreTV",
                icon: "fa-television",
                links: [
                    { url: "https://libretv-jsh40svv.edgeone.app/", platform: "EdgeOne", responseTime: null, status: 'unknown' },
                    { url: "https://libretv-3fh.pages.dev/", platform: "Cloudflare", responseTime: null, status: 'unknown' },
                    { url: "https://libretv.magiccode.dpdns.org/", platform: "MagicCode", responseTime: null, status: 'unknown' }
                ]
            },
            {
                name: "MoonTV",
                icon: "fa-moon-o",
                links: [
                    { url: "https://moontv-bnx.pages.dev/", platform: "Cloudflare", responseTime: null, status: 'unknown' },
                    { url: "https://moontv.magiccode.dpdns.org/", platform: "MagicCode", responseTime: null, status: 'unknown' }
                ]
            }
        ];

        // DOM元素
        const refreshBtn = document.getElementById('refresh-btn');
        const lastCheckedEl = document.querySelector('#last-checked span');
        const notificationContainer = document.getElementById('notification-container');
        const sitesContainer = document.getElementById('sites-container');

        // 统计面板元素
        const totalLinksEl = document.getElementById('total-links');
        const onlineLinksEl = document.getElementById('online-links');
        const offlineLinksEl = document.getElementById('offline-links');
        const successRateEl = document.getElementById('success-rate');

        // 通知计数器，用于生成唯一ID
        let notificationCounter = 0;

        // 显示通知
        function showNotification(message, type = 'success', duration = 3000) {
            console.log('显示通知:', message, type, duration); // 调试信息
            const notificationId = `notification-${++notificationCounter}`;

            // 检查容器是否存在
            if (!notificationContainer) {
                console.error('通知容器不存在!');
                return;
            }

            // 创建通知元素
            const notification = document.createElement('div');
            notification.id = notificationId;
            notification.className = `
                bg-white text-gray-800 px-4 py-3 rounded-lg shadow-lg
                transform translate-x-full opacity-0 transition-all duration-300
                flex items-center min-w-64 max-w-80 pointer-events-auto
                border-l-4 ${getNotificationStyle(type)}
            `.trim().replace(/\s+/g, ' ');

            // 确保通知在最顶层
            notification.style.zIndex = '10000';

            // 设置图标
            const icon = getNotificationIcon(type);

            // 设置内容
            notification.innerHTML = `
                <i class="fa ${icon} mr-3 text-lg"></i>
                <span class="flex-1 text-sm font-medium">${message}</span>
                <button onclick="removeNotification('${notificationId}')"
                        class="ml-2 text-gray-400 hover:text-gray-600 transition-colors">
                    <i class="fa fa-times"></i>
                </button>
            `;

            // 添加到容器
            notificationContainer.appendChild(notification);

            // 触发进入动画
            setTimeout(() => {
                notification.classList.remove('translate-x-full', 'opacity-0');
                notification.classList.add('translate-x-0', 'opacity-100');
            }, 10);

            // 自动移除
            setTimeout(() => {
                removeNotification(notificationId);
            }, duration);

            return notificationId;
        }

        // 获取通知样式
        function getNotificationStyle(type) {
            switch (type) {
                case 'success':
                    return 'border-green-500';
                case 'error':
                    return 'border-red-500';
                case 'warning':
                    return 'border-yellow-500';
                case 'info':
                    return 'border-blue-500';
                default:
                    return 'border-green-500';
            }
        }

        // 获取通知图标
        function getNotificationIcon(type) {
            switch (type) {
                case 'success':
                    return 'fa-check-circle text-green-500';
                case 'error':
                    return 'fa-exclamation-circle text-red-500';
                case 'warning':
                    return 'fa-exclamation-triangle text-yellow-500';
                case 'info':
                    return 'fa-info-circle text-blue-500';
                default:
                    return 'fa-check-circle text-green-500';
            }
        }

        // 移除通知
        function removeNotification(notificationId) {
            const notification = document.getElementById(notificationId);
            if (notification) {
                notification.classList.remove('translate-x-0', 'opacity-100');
                notification.classList.add('translate-x-full', 'opacity-0');

                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }
        }

        // 清除所有通知
        function clearAllNotifications() {
            const notifications = notificationContainer.querySelectorAll('[id^="notification-"]');
            notifications.forEach(notification => {
                removeNotification(notification.id);
            });
        }

        // 排序站点链接 - 按响应时间和状态排序
        function sortSiteLinks(site) {
            site.links.sort((a, b) => {
                // 首先按状态排序：在线 > 离线/超时 > 未知
                const statusPriority = {
                    'online': 1,
                    'offline': 2,
                    'timeout': 2,
                    'unknown': 3
                };

                const statusDiff = statusPriority[a.status] - statusPriority[b.status];
                if (statusDiff !== 0) {
                    return statusDiff;
                }

                // 如果状态相同，按响应时间排序（在线状态下）
                if (a.status === 'online' && b.status === 'online') {
                    if (a.responseTime !== null && b.responseTime !== null) {
                        return a.responseTime - b.responseTime;
                    }
                    if (a.responseTime !== null) return -1;
                    if (b.responseTime !== null) return 1;
                }

                // 其他情况保持原顺序
                return 0;
            });
        }

        // 更新统计信息
        function updateStatistics() {
            let totalLinks = 0;
            let onlineLinks = 0;
            let offlineLinks = 0;

            // 计算总链接数
            sites.forEach(site => {
                totalLinks += site.links.length;
            });

            // 统计在线和离线链接数
            sites.forEach(site => {
                const linksContainer = document.getElementById(`${site.name.toLowerCase()}-links`);
                if (linksContainer) {
                    const linkItems = linksContainer.querySelectorAll('.link-item');
                    linkItems.forEach(linkItem => {
                        const statusText = linkItem.querySelector('.status-indicator span:last-child');
                        if (statusText) {
                            const status = statusText.textContent.toLowerCase();
                            if (status.includes('在线')) {
                                onlineLinks++;
                            } else if (status.includes('离线') || status.includes('超时')) {
                                offlineLinks++;
                            }
                        }
                    });
                }
            });

            // 计算成功率
            const checkedLinks = onlineLinks + offlineLinks;
            const successRate = checkedLinks > 0 ? Math.round((onlineLinks / checkedLinks) * 100) : 0;

            // 更新UI
            totalLinksEl.textContent = totalLinks;
            onlineLinksEl.textContent = onlineLinks;
            offlineLinksEl.textContent = offlineLinks;
            successRateEl.textContent = `${successRate}%`;
        }

        // 生成单个站点的链接HTML
        function generateSiteLinksHTML(site) {
            // 找到最快的在线链接
            const onlineLinks = site.links.filter(link => link.status === 'online' && link.responseTime !== null);
            const fastestLink = onlineLinks.length > 0 ?
                onlineLinks.reduce((fastest, current) =>
                    current.responseTime < fastest.responseTime ? current : fastest
                ) : null;

            return site.links.map((link, index) => {
                const isFastest = fastestLink && link.url === fastestLink.url;
                const isOnline = link.status === 'online';

                // 根据状态生成状态显示内容
                let statusContent = '';
                let statusDotClass = 'w-3 h-3 rounded-full bg-gray-400 mr-2 status-pulse';
                let statusText = '检查中...';
                let statusTextClass = 'text-sm text-gray-500';

                if (link.status === 'online') {
                    statusDotClass = 'w-3 h-3 rounded-full bg-success mr-2';
                    statusText = link.responseTime ? `在线 (${link.responseTime}ms)` : '在线';
                    statusTextClass = 'text-sm text-success font-medium';
                } else if (link.status === 'offline') {
                    statusDotClass = 'w-3 h-3 rounded-full bg-danger mr-2';
                    statusText = '离线';
                    statusTextClass = 'text-sm text-danger font-medium';
                } else if (link.status === 'timeout') {
                    statusDotClass = 'w-3 h-3 rounded-full bg-warning mr-2';
                    statusText = '超时';
                    statusTextClass = 'text-sm text-warning font-medium';
                }

                return `
                <li class="link-item cursor-pointer" data-url="${link.url}" title="点击访问 ${link.platform} 线路">
                    <div class="flex flex-col sm:flex-row sm:items-center justify-between bg-gray-50 p-4 rounded-lg border border-gray-200 hover:border-primary/30 hover:bg-primary/5 transition-all ${isFastest ? 'ring-2 ring-success/20 bg-success/5' : ''}">
                        <div class="mb-2 sm:mb-0 flex-1">
                            <div class="font-medium mb-1 text-gray-800 flex items-center">
                                线路${index + 1} (${link.platform})
                                ${isFastest ?
                                    `<span class="ml-2 text-xs bg-success text-white px-2 py-1 rounded-full flex items-center">
                                        <i class="fa fa-bolt mr-1"></i>最快
                                    </span>` :
                                    isOnline ?
                                    `<span class="ml-2 text-xs bg-success/10 text-success px-2 py-1 rounded-full">可用</span>` : ''}
                            </div>
                        </div>
                        <div class="status-indicator flex items-center justify-between">
                            <div class="flex items-center mr-3">
                                <span class="${statusDotClass}"></span>
                                <span class="${statusTextClass}">${statusText}</span>
                            </div>
                            <button class="check-single-btn text-xs text-primary hover:text-primary/80 px-2 py-1 rounded border border-primary/20 hover:border-primary/40 transition-all"
                                    data-site="${site.name.toLowerCase()}" data-index="${index}" title="单独检查此线路">
                                <i class="fa fa-refresh"></i>
                            </button>
                        </div>
                    </div>
                </li>
                `;
            }).join('');
        }

        // 重新生成单个站点的链接列表
        function regenerateSiteLinks(site) {
            const linksContainer = document.getElementById(`${site.name.toLowerCase()}-links`);
            if (linksContainer) {
                linksContainer.innerHTML = generateSiteLinksHTML(site);
            }
        }

        // 生成站点卡片HTML
        function generateSiteCards() {
            sitesContainer.innerHTML = '';

            sites.forEach(site => {
                const siteCard = document.createElement('div');
                siteCard.className = 'bg-white rounded-xl shadow-lg overflow-hidden card-hover';

                // 站点头部
                siteCard.innerHTML = `
          <div class="bg-primary/10 px-6 py-4 border-b border-primary/20">
            <div class="flex justify-between items-center">
              <h3 class="text-xl font-bold text-primary flex items-center">
                <i class="fa ${site.icon} mr-3 text-2xl"></i>
                ${site.name}
              </h3>
              <span class="bg-primary/20 text-primary px-3 py-1 rounded-full text-sm font-medium">
                ${site.links.length}条线路
              </span>
            </div>
          </div>

          <div class="p-6">
            <!-- 线路列表 -->
            <div>
              <h4 class="font-semibold text-gray-700 mb-4">可用线路 <span class="text-xs text-gray-500">(按速度排序)</span></h4>
              <ul class="space-y-4" id="${site.name.toLowerCase()}-links">
                ${generateSiteLinksHTML(site)}
              </ul>
            </div>
          </div>
        `;

                sitesContainer.appendChild(siteCard);
            });
        }

        // 检测链接状态 - 改进版本
        async function checkLinkStatus(url, timeout = 10000) {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), timeout);

            try {
                // 尝试多种检测方法
                const methods = [
                    // 方法1: 使用GET请求检查（最可靠）
                    async () => {
                        const response = await fetch(url, {
                            method: 'GET',
                            signal: controller.signal,
                            cache: 'no-store',
                            redirect: 'follow'
                        });
                        return { success: response.ok, status: response.status, method: 'GET' };
                    },

                    // 方法2: 使用HEAD请求检查
                    async () => {
                        const response = await fetch(url, {
                            method: 'HEAD',
                            signal: controller.signal,
                            cache: 'no-store',
                            redirect: 'follow'
                        });
                        return { success: response.ok, status: response.status, method: 'HEAD' };
                    },

                    // 方法3: 使用no-cors模式（作为备选）
                    async () => {
                        const response = await fetch(url, {
                            method: 'GET',
                            mode: 'no-cors',
                            signal: controller.signal,
                            cache: 'no-store'
                        });
                        // no-cors模式下，只要没有抛出异常就认为可访问
                        return { success: true, status: 'opaque', method: 'no-cors' };
                    }
                ];

                // 依次尝试各种方法
                for (const method of methods) {
                    try {
                        const result = await method();
                        clearTimeout(timeoutId);
                        console.log(`URL: ${url}, Method: ${result.method}, Status: ${result.status}, Success: ${result.success}`);
                        return result.success;
                    } catch (error) {
                        // 如果当前方法失败，继续尝试下一个方法
                        console.log(`Method failed for ${url}:`, error.message);
                        continue;
                    }
                }

                // 所有方法都失败
                clearTimeout(timeoutId);
                return false;

            } catch (error) {
                clearTimeout(timeoutId);
                console.error(`All methods failed for ${url}:`, error);
                return false;
            }
        }

        // 带重试的链接状态检测
        async function checkLinkStatusWithRetry(url, maxRetries = 2) {
            for (let attempt = 1; attempt <= maxRetries; attempt++) {
                console.log(`Checking ${url} - Attempt ${attempt}/${maxRetries}`);

                const isOnline = await checkLinkStatus(url);

                if (isOnline) {
                    return true;
                }

                // 如果不是最后一次尝试，等待一段时间后重试
                if (attempt < maxRetries) {
                    await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
                }
            }

            return false;
        }

        // 更新单个链接状态的UI
        function updateLinkStatusUI(linkElement, status, responseTime = null) {
            const statusIndicator = linkElement.querySelector('.status-indicator');
            const statusDot = statusIndicator.querySelector('span:first-child');
            const statusText = statusIndicator.querySelector('span:last-child');

            // 移除脉冲动画
            statusDot.classList.remove('status-pulse');

            if (status === 'checking') {
                statusDot.className = 'w-3 h-3 rounded-full bg-gray-400 mr-2 status-pulse';
                statusText.textContent = '检查中...';
                statusText.className = 'text-sm text-gray-500';
            } else if (status === 'online') {
                statusDot.className = 'w-3 h-3 rounded-full bg-success mr-2';
                const timeText = responseTime ? ` (${responseTime}ms)` : '';
                statusText.textContent = `在线${timeText}`;
                statusText.className = 'text-sm text-success font-medium';
            } else if (status === 'offline') {
                statusDot.className = 'w-3 h-3 rounded-full bg-danger mr-2';
                statusText.textContent = '离线';
                statusText.className = 'text-sm text-danger font-medium';
            } else if (status === 'timeout') {
                statusDot.className = 'w-3 h-3 rounded-full bg-warning mr-2';
                statusText.textContent = '超时';
                statusText.className = 'text-sm text-warning font-medium';
            }
        }

        // 检测单个链接并更新UI
        async function checkAndUpdateSingleLink(site, linkIndex) {
            const linksContainer = document.getElementById(`${site.name.toLowerCase()}-links`);
            const linkItem = linksContainer.querySelector(`.link-item:nth-child(${linkIndex + 1})`);
            const url = site.links[linkIndex].url;

            // 设置检查中状态
            updateLinkStatusUI(linkItem, 'checking');

            try {
                const startTime = Date.now();
                const isOnline = await checkLinkStatusWithRetry(url);
                const responseTime = Date.now() - startTime;

                // 更新链接数据
                if (isOnline) {
                    site.links[linkIndex].status = 'online';
                    site.links[linkIndex].responseTime = responseTime;
                    updateLinkStatusUI(linkItem, 'online', responseTime);
                } else {
                    site.links[linkIndex].status = 'offline';
                    site.links[linkIndex].responseTime = null;
                    updateLinkStatusUI(linkItem, 'offline');
                }

                // 排序并重新生成该站点的链接列表
                sortSiteLinks(site);
                regenerateSiteLinks(site);

                // 更新统计信息
                updateStatistics();

                return isOnline;
            } catch (error) {
                console.error(`Error checking ${url}:`, error);
                site.links[linkIndex].status = 'timeout';
                site.links[linkIndex].responseTime = null;
                updateLinkStatusUI(linkItem, 'timeout');

                // 排序并重新生成该站点的链接列表
                sortSiteLinks(site);
                regenerateSiteLinks(site);

                return false;
            }
        }

        // 更新所有链接状态 - 改进版本
        async function updateLinkStatuses() {
            // 显示刷新中状态
            refreshBtn.disabled = true;
            refreshBtn.innerHTML = '<i class="fa fa-spinner fa-spin mr-2"></i><span>检查中...</span>';

            // 更新最后检查时间
            const now = new Date();
            const formattedTime = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            lastCheckedEl.textContent = `最后检查: ${formattedTime}`;

            // 统计信息
            let totalLinks = 0;
            let onlineLinks = 0;
            let checkedLinks = 0;

            // 计算总链接数
            sites.forEach(site => {
                totalLinks += site.links.length;
            });

            // 更新进度显示
            function updateProgress() {
                const progress = Math.round((checkedLinks / totalLinks) * 100);
                refreshBtn.innerHTML = `<i class="fa fa-spinner fa-spin mr-2"></i><span>检查中... ${progress}%</span>`;
            }

            try {
                // 并发检查所有链接（限制并发数量以避免过载）
                const concurrencyLimit = 3;
                const checkPromises = [];

                for (const site of sites) {
                    for (let i = 0; i < site.links.length; i++) {
                        const checkPromise = (async () => {
                            const url = site.links[i].url;

                            const startTime = Date.now();
                            const isOnline = await checkLinkStatusWithRetry(url);
                            const responseTime = Date.now() - startTime;

                            // 更新链接数据
                            if (isOnline) {
                                site.links[i].status = 'online';
                                site.links[i].responseTime = responseTime;
                            } else {
                                site.links[i].status = 'offline';
                                site.links[i].responseTime = null;
                            }

                            checkedLinks++;
                            if (isOnline) onlineLinks++;
                            updateProgress();
                            return isOnline;
                        })();

                        checkPromises.push(checkPromise);

                        // 控制并发数量
                        if (checkPromises.length >= concurrencyLimit) {
                            await Promise.all(checkPromises.splice(0, concurrencyLimit));
                        }
                    }
                }

                // 等待剩余的检查完成
                if (checkPromises.length > 0) {
                    await Promise.all(checkPromises);
                }

                // 所有检查完成后，对所有站点进行排序并重新生成
                sites.forEach(site => {
                    sortSiteLinks(site);
                    regenerateSiteLinks(site);
                });

                // 显示完成状态
                const successRate = Math.round((onlineLinks / totalLinks) * 100);
                let statusMessage = `状态检查完成！${onlineLinks}/${totalLinks} 条线路在线 (${successRate}%)`;

                // 根据成功率显示不同的状态信息
                if (successRate >= 80) {
                    statusMessage += ' 🟢';
                } else if (successRate >= 50) {
                    statusMessage += ' 🟡';
                } else {
                    statusMessage += ' 🔴';
                }

                showNotification(statusMessage, 'success', 5000);

                // 如果成功率较低，建议用户稍后重试
                if (successRate < 50 && onlineLinks > 0) {
                    setTimeout(() => {
                        showNotification('检测到部分线路可能暂时不可用，建议5分钟后重新检查', 'warning', 5000);
                    }, 4000);
                }

            } catch (error) {
                console.error('Error during status update:', error);
                showNotification('状态检查过程中出现错误，请稍后重试', 'error');
            } finally {
                // 恢复刷新按钮
                refreshBtn.disabled = false;
                refreshBtn.innerHTML = '<i class="fa fa-refresh mr-2"></i><span>刷新状态</span>';

                // 最终更新统计信息
                updateStatistics();
            }
        }

        // 单独检查某个链接
        async function checkSingleLink(siteName, originalIndex) {
            const site = sites.find(s => s.name.toLowerCase() === siteName);
            if (!site) {
                console.error('Invalid site');
                return;
            }

            // 找到当前显示顺序中对应的链接
            const linksContainer = document.getElementById(`${siteName}-links`);
            const linkItems = linksContainer.querySelectorAll('.link-item');

            if (!linkItems[originalIndex]) {
                console.error('Invalid link index');
                return;
            }

            const linkItem = linkItems[originalIndex];
            const url = linkItem.dataset.url;
            const linkData = site.links.find(link => link.url === url);

            if (!linkData) {
                console.error('Link data not found');
                return;
            }

            // 设置检查中状态
            updateLinkStatusUI(linkItem, 'checking');

            try {
                const startTime = Date.now();
                const isOnline = await checkLinkStatusWithRetry(url);
                const responseTime = Date.now() - startTime;

                // 更新链接数据
                if (isOnline) {
                    linkData.status = 'online';
                    linkData.responseTime = responseTime;
                } else {
                    linkData.status = 'offline';
                    linkData.responseTime = null;
                }

                // 排序并重新生成该站点的链接列表
                sortSiteLinks(site);
                regenerateSiteLinks(site);

                // 更新统计信息
                updateStatistics();

                showNotification(`${site.name} 线路状态已更新`);
            } catch (error) {
                console.error(`Error checking ${url}:`, error);
                linkData.status = 'timeout';
                linkData.responseTime = null;

                // 排序并重新生成该站点的链接列表
                sortSiteLinks(site);
                regenerateSiteLinks(site);

                showNotification(`${site.name} 线路检查失败`, 'error');
            }
        }

        // 自动定时检查功能
        let autoCheckInterval = null;

        function startAutoCheck(intervalMinutes = 30) {
            if (autoCheckInterval) {
                clearInterval(autoCheckInterval);
            }

            autoCheckInterval = setInterval(() => {
                console.log('Auto checking link statuses...');
                updateLinkStatuses();
            }, intervalMinutes * 60 * 1000);

            console.log(`Auto check started, interval: ${intervalMinutes} minutes`);
        }

        function stopAutoCheck() {
            if (autoCheckInterval) {
                clearInterval(autoCheckInterval);
                autoCheckInterval = null;
                console.log('Auto check stopped');
            }
        }

        // 添加键盘快捷键支持
        function setupKeyboardShortcuts() {
            document.addEventListener('keydown', (e) => {
                // Ctrl+R 或 F5: 刷新状态
                if ((e.ctrlKey && e.key === 'r') || e.key === 'F5') {
                    e.preventDefault();
                    if (!refreshBtn.disabled) {
                        updateLinkStatuses();
                    }
                }

                // Ctrl+Shift+R: 强制刷新所有状态
                if (e.ctrlKey && e.shiftKey && e.key === 'R') {
                    e.preventDefault();
                    if (!refreshBtn.disabled) {
                        // 清除所有缓存并重新检查
                        location.reload();
                    }
                }
            });
        }

        // 初始化页面
        function init() {
            // 显示加载动画
            showLoadingOverlay();

            // 延迟加载内容以显示加载动画
            setTimeout(() => {
                // 生成站点卡片
                generateSiteCards();

                // 初始化统计信息
                updateStatistics();

                // 隐藏加载动画并显示内容
                hideLoadingOverlay();

                // 测试通知系统
                setTimeout(() => {
                    showNotification('页面加载完成，通知系统正常工作！', 'info', 4000);
                }, 1000);
            }, 800);

            // 添加刷新按钮事件监听
            refreshBtn.addEventListener('click', updateLinkStatuses);

            // 添加点击事件监听
            document.addEventListener('click', (e) => {
                // 处理单独检查按钮点击
                if (e.target.closest('.check-single-btn')) {
                    e.stopPropagation(); // 阻止事件冒泡，避免触发li的点击事件
                    const btn = e.target.closest('.check-single-btn');
                    const siteName = btn.dataset.site;
                    const linkIndex = parseInt(btn.dataset.index);
                    checkSingleLink(siteName, linkIndex);
                    return;
                }

                // 处理线路项点击（打开新标签）
                if (e.target.closest('.link-item')) {
                    const linkItem = e.target.closest('.link-item');
                    const url = linkItem.dataset.url;

                    if (url) {
                        // 在新标签中打开链接
                        window.open(url, '_blank', 'noopener,noreferrer');

                        // 显示通知
                        const platform = linkItem.querySelector('.font-medium').textContent;
                        showNotification(`正在打开 ${platform}...`, 'info', 2000);
                    }
                }
            });

            // 设置键盘快捷键
            setupKeyboardShortcuts();

            // 启动自动检查（30分钟间隔）
            startAutoCheck(30);

            // 页面加载完成后自动检查一次状态
            setTimeout(updateLinkStatuses, 1000);

            // 页面可见性变化时的处理
            document.addEventListener('visibilitychange', handleVisibilityChange);

            // 预加载资源
            preloadResources();

            // 懒加载图片
            lazyLoadImages();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);

        // PWA 功能初始化
        initPWA();

        // 离线功能初始化
        initOfflineFeatures();

        // PWA 相关功能
        function initPWA() {
            // 注册 Service Worker
            if ('serviceWorker' in navigator) {
                window.addEventListener('load', () => {
                    navigator.serviceWorker.register('/sw.js')
                        .then(registration => {
                            console.log('SW registered: ', registration);

                            // 检查更新
                            registration.addEventListener('updatefound', () => {
                                const newWorker = registration.installing;
                                newWorker.addEventListener('statechange', () => {
                                    if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                        showUpdateNotification();
                                    }
                                });
                            });
                        })
                        .catch(registrationError => {
                            console.log('SW registration failed: ', registrationError);
                        });
                });
            }

            // 处理PWA安装提示
            let deferredPrompt;
            window.addEventListener('beforeinstallprompt', (e) => {
                e.preventDefault();
                deferredPrompt = e;
                showInstallPrompt();
            });

            // 处理PWA安装完成
            window.addEventListener('appinstalled', () => {
                console.log('PWA was installed');
                showNotification('应用已成功安装到设备！', 'success', 4000);
                hideInstallPrompt();
            });

            // 处理Service Worker消息
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.addEventListener('message', event => {
                    if (event.data && event.data.type === 'BACKGROUND_SYNC_COMPLETE') {
                        console.log('Background sync completed');
                        // 可以在这里更新UI或显示通知
                    }
                });
            }

            // 检查URL参数
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('action') === 'refresh') {
                // 从快捷方式启动的刷新操作
                setTimeout(() => {
                    updateLinkStatuses();
                }, 1000);
            }
        }

        // 显示更新通知
        function showUpdateNotification() {
            const updateBanner = document.createElement('div');
            updateBanner.id = 'update-banner';
            updateBanner.className = 'fixed top-0 left-0 right-0 bg-primary text-white p-4 z-50 transform -translate-y-full transition-transform duration-300';
            updateBanner.innerHTML = `
                <div class="container mx-auto flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fa fa-download mr-2"></i>
                        <span>发现新版本，点击更新以获得最新功能</span>
                    </div>
                    <div>
                        <button onclick="updateApp()" class="bg-white text-primary px-4 py-2 rounded mr-2 hover:bg-gray-100">
                            立即更新
                        </button>
                        <button onclick="hideUpdateBanner()" class="text-white hover:text-gray-200">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(updateBanner);

            setTimeout(() => {
                updateBanner.classList.remove('-translate-y-full');
            }, 100);
        }

        // 更新应用
        function updateApp() {
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.getRegistration().then(registration => {
                    if (registration && registration.waiting) {
                        registration.waiting.postMessage({ type: 'SKIP_WAITING' });
                        window.location.reload();
                    }
                });
            }
        }

        // 隐藏更新横幅
        function hideUpdateBanner() {
            const banner = document.getElementById('update-banner');
            if (banner) {
                banner.classList.add('-translate-y-full');
                setTimeout(() => {
                    banner.remove();
                }, 300);
            }
        }

        // 显示安装提示
        function showInstallPrompt() {
            // 检查是否已经安装或已经显示过提示
            if (localStorage.getItem('pwa-install-dismissed') === 'true') {
                return;
            }

            const installBanner = document.createElement('div');
            installBanner.id = 'install-banner';
            installBanner.className = 'fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 z-50 transform translate-y-full transition-transform duration-300 shadow-lg';
            installBanner.innerHTML = `
                <div class="container mx-auto flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center mr-3">
                            <i class="fa fa-mobile text-white text-xl"></i>
                        </div>
                        <div>
                            <div class="font-semibold text-gray-800">安装到设备</div>
                            <div class="text-sm text-gray-600">添加到主屏幕，随时快速访问</div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button onclick="installPWA()" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors">
                            安装
                        </button>
                        <button onclick="dismissInstallPrompt()" class="text-gray-500 hover:text-gray-700 p-2">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(installBanner);

            setTimeout(() => {
                installBanner.classList.remove('translate-y-full');
            }, 100);

            // 5秒后自动隐藏
            setTimeout(() => {
                if (document.getElementById('install-banner')) {
                    dismissInstallPrompt();
                }
            }, 10000);
        }

        // 安装PWA
        function installPWA() {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        console.log('User accepted the install prompt');
                    } else {
                        console.log('User dismissed the install prompt');
                    }
                    deferredPrompt = null;
                    hideInstallPrompt();
                });
            }
        }

        // 忽略安装提示
        function dismissInstallPrompt() {
            localStorage.setItem('pwa-install-dismissed', 'true');
            hideInstallPrompt();
        }

        // 隐藏安装提示
        function hideInstallPrompt() {
            const banner = document.getElementById('install-banner');
            if (banner) {
                banner.classList.add('translate-y-full');
                setTimeout(() => {
                    banner.remove();
                }, 300);
            }
        }

        // 离线功能
        function initOfflineFeatures() {
            // 添加网络状态指示器
            addNetworkStatusIndicator();

            // 监听网络状态变化
            window.addEventListener('online', handleOnline);
            window.addEventListener('offline', handleOffline);

            // 初始化时检查网络状态
            if (!navigator.onLine) {
                handleOffline();
            }

            // 缓存站点数据到localStorage
            cacheSiteData();

            // 定期同步缓存数据
            setInterval(syncCachedData, 5 * 60 * 1000); // 每5分钟同步一次
        }

        // 添加网络状态指示器
        function addNetworkStatusIndicator() {
            const indicator = document.createElement('div');
            indicator.id = 'network-status';
            indicator.className = 'fixed top-20 right-4 z-40 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 transform translate-x-full';
            document.body.appendChild(indicator);
        }

        // 处理在线状态
        function handleOnline() {
            console.log('Network: Online');
            const indicator = document.getElementById('network-status');
            if (indicator) {
                indicator.className = 'fixed top-20 right-4 z-40 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 bg-success text-white';
                indicator.innerHTML = '<i class="fa fa-wifi mr-1"></i>已连接';

                // 3秒后隐藏
                setTimeout(() => {
                    indicator.classList.add('translate-x-full');
                }, 3000);
            }

            // 网络恢复时自动刷新状态
            if (document.querySelector('#last-checked span').textContent.includes('离线')) {
                setTimeout(() => {
                    updateLinkStatuses();
                }, 1000);
            }

            // 同步缓存数据
            syncCachedData();
        }

        // 处理离线状态
        function handleOffline() {
            console.log('Network: Offline');
            const indicator = document.getElementById('network-status');
            if (indicator) {
                indicator.className = 'fixed top-20 right-4 z-40 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 bg-warning text-white';
                indicator.innerHTML = '<i class="fa fa-exclamation-triangle mr-1"></i>离线模式';
                indicator.classList.remove('translate-x-full');
            }

            // 更新最后检查时间为离线状态
            lastCheckedEl.textContent = '最后检查: 离线模式';

            // 禁用刷新按钮
            refreshBtn.disabled = true;
            refreshBtn.innerHTML = '<i class="fa fa-exclamation-triangle mr-2"></i><span>离线模式</span>';

            // 从缓存加载数据
            loadCachedData();

            showNotification('已切换到离线模式，显示缓存数据', 'warning', 4000);
        }

        // 缓存站点数据
        function cacheSiteData() {
            try {
                const dataToCache = {
                    sites: sites,
                    lastUpdate: Date.now(),
                    version: '1.0.0'
                };
                localStorage.setItem('video-nav-cache', JSON.stringify(dataToCache));
                console.log('Site data cached successfully');
            } catch (error) {
                console.error('Failed to cache site data:', error);
            }
        }

        // 加载缓存数据
        function loadCachedData() {
            try {
                const cachedData = localStorage.getItem('video-nav-cache');
                if (cachedData) {
                    const data = JSON.parse(cachedData);

                    // 检查缓存是否过期（24小时）
                    const cacheAge = Date.now() - data.lastUpdate;
                    const maxAge = 24 * 60 * 60 * 1000; // 24小时

                    if (cacheAge < maxAge && data.sites) {
                        // 使用缓存数据更新站点信息
                        data.sites.forEach((cachedSite, index) => {
                            if (sites[index]) {
                                sites[index].links.forEach((link, linkIndex) => {
                                    if (cachedSite.links[linkIndex]) {
                                        link.status = cachedSite.links[linkIndex].status || 'unknown';
                                        link.responseTime = cachedSite.links[linkIndex].responseTime || null;
                                    }
                                });
                            }
                        });

                        // 重新生成站点卡片
                        sites.forEach(site => {
                            sortSiteLinks(site);
                            regenerateSiteLinks(site);
                        });

                        // 更新统计信息
                        updateStatistics();

                        const cacheDate = new Date(data.lastUpdate).toLocaleString('zh-CN');
                        lastCheckedEl.textContent = `最后检查: ${cacheDate} (缓存)`;

                        console.log('Loaded cached site data');
                    }
                }
            } catch (error) {
                console.error('Failed to load cached data:', error);
            }
        }

        // 同步缓存数据
        function syncCachedData() {
            if (navigator.onLine) {
                cacheSiteData();
            }
        }

        // 离线时的链接检测（使用缓存数据）
        function checkLinkStatusOffline(url) {
            try {
                const cachedData = localStorage.getItem('video-nav-cache');
                if (cachedData) {
                    const data = JSON.parse(cachedData);
                    for (const site of data.sites) {
                        const link = site.links.find(l => l.url === url);
                        if (link) {
                            return {
                                status: link.status || 'unknown',
                                responseTime: link.responseTime || null,
                                cached: true
                            };
                        }
                    }
                }
            } catch (error) {
                console.error('Error checking offline status:', error);
            }

            return {
                status: 'unknown',
                responseTime: null,
                cached: false
            };
        }

        // 加载动画控制
        function showLoadingOverlay(text = '正在加载应用...') {
            const overlay = document.getElementById('loading-overlay');
            const subtext = overlay.querySelector('.loading-subtext');
            if (overlay && subtext) {
                subtext.textContent = text;
                overlay.classList.remove('hidden');
            }
        }

        function hideLoadingOverlay() {
            const overlay = document.getElementById('loading-overlay');
            const pageContent = document.getElementById('page-content');

            if (overlay) {
                overlay.classList.add('hidden');
            }

            if (pageContent) {
                pageContent.classList.add('loaded');

                // 逐个显示站点卡片
                const siteCards = document.querySelectorAll('.bg-white.rounded-xl.shadow-lg');
                siteCards.forEach((card, index) => {
                    card.classList.add('site-card');
                    setTimeout(() => {
                        card.classList.add('animate-in');
                    }, index * 150);
                });
            }
        }

        // 性能优化：防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // 性能优化：节流函数
        function throttle(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            }
        }

        // 预加载关键资源
        function preloadResources() {
            const resources = [
                'https://cdn.tailwindcss.com',
                'https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css'
            ];

            resources.forEach(url => {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.as = 'style';
                link.href = url;
                document.head.appendChild(link);
            });
        }

        // 懒加载图片
        function lazyLoadImages() {
            const images = document.querySelectorAll('img[data-src]');
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            images.forEach(img => imageObserver.observe(img));
        }

        // 优化的链接状态更新（使用防抖）
        const debouncedUpdateStatistics = debounce(updateStatistics, 300);

        // 页面可见性API优化
        function handleVisibilityChange() {
            if (document.hidden) {
                // 页面隐藏时暂停某些操作
                stopAutoCheck();
            } else {
                // 页面重新可见时恢复操作
                startAutoCheck(30);

                // 检查是否需要更新数据
                const lastCheck = lastCheckedEl.textContent;
                if (lastCheck.includes('从未') || shouldRefreshData()) {
                    updateLinkStatuses();
                }
            }
        }

        // 判断是否需要刷新数据
        function shouldRefreshData() {
            const lastCheckText = lastCheckedEl.textContent;
            if (lastCheckText.includes('从未') || lastCheckText.includes('离线')) {
                return true;
            }

            // 解析最后检查时间
            const timeMatch = lastCheckText.match(/(\d{4}\/\d{1,2}\/\d{1,2}\s+\d{1,2}:\d{2}:\d{2})/);
            if (timeMatch) {
                const lastCheckTime = new Date(timeMatch[1]);
                const now = new Date();
                const timeDiff = now - lastCheckTime;

                // 如果超过30分钟则需要刷新
                return timeDiff > 30 * 60 * 1000;
            }

            return false;
        }
    </script>
</body>

</html>