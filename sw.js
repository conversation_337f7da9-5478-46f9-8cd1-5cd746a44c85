// Service Worker for 免费视频资源导航 PWA
const CACHE_NAME = 'video-nav-v1.0.0';
const OFFLINE_URL = '/offline.html';

// 需要缓存的资源
const CACHE_URLS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/offline.html',
  // 外部CDN资源
  'https://cdn.tailwindcss.com',
  'https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css'
];

// 安装事件 - 预缓存资源
self.addEventListener('install', event => {
  console.log('Service Worker installing...');
  
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('Caching app shell');
        return cache.addAll(CACHE_URLS);
      })
      .then(() => {
        // 强制激活新的Service Worker
        return self.skipWaiting();
      })
      .catch(error => {
        console.error('Failed to cache resources:', error);
      })
  );
});

// 激活事件 - 清理旧缓存
self.addEventListener('activate', event => {
  console.log('Service Worker activating...');
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (cacheName !== CACHE_NAME) {
              console.log('Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        // 立即控制所有客户端
        return self.clients.claim();
      })
  );
});

// 获取事件 - 网络优先策略，带离线回退
self.addEventListener('fetch', event => {
  // 只处理GET请求
  if (event.request.method !== 'GET') {
    return;
  }

  // 跳过chrome-extension和其他非http(s)请求
  if (!event.request.url.startsWith('http')) {
    return;
  }

  event.respondWith(
    handleFetch(event.request)
  );
});

async function handleFetch(request) {
  const url = new URL(request.url);
  
  try {
    // 对于HTML页面，使用网络优先策略
    if (request.headers.get('accept')?.includes('text/html')) {
      return await handleHTMLRequest(request);
    }
    
    // 对于静态资源，使用缓存优先策略
    if (isStaticResource(url)) {
      return await handleStaticResource(request);
    }
    
    // 对于API请求，使用网络优先策略
    return await handleAPIRequest(request);
    
  } catch (error) {
    console.error('Fetch error:', error);
    return await handleOfflineResponse(request);
  }
}

// 处理HTML请求 - 网络优先，缓存回退
async function handleHTMLRequest(request) {
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // 更新缓存
      const cache = await caches.open(CACHE_NAME);
      cache.put(request, networkResponse.clone());
      return networkResponse;
    }
    
    throw new Error('Network response not ok');
  } catch (error) {
    // 网络失败，尝试从缓存获取
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // 如果是根路径，返回离线页面
    if (request.url.endsWith('/') || request.url.includes('index.html')) {
      const offlineResponse = await caches.match(OFFLINE_URL);
      if (offlineResponse) {
        return offlineResponse;
      }
    }
    
    throw error;
  }
}

// 处理静态资源 - 缓存优先，网络回退
async function handleStaticResource(request) {
  const cachedResponse = await caches.match(request);
  if (cachedResponse) {
    return cachedResponse;
  }
  
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    throw error;
  }
}

// 处理API请求 - 网络优先
async function handleAPIRequest(request) {
  try {
    const networkResponse = await fetch(request);
    return networkResponse;
  } catch (error) {
    // API请求失败时，可以返回缓存的数据或错误响应
    throw error;
  }
}

// 处理离线响应
async function handleOfflineResponse(request) {
  if (request.headers.get('accept')?.includes('text/html')) {
    const offlineResponse = await caches.match(OFFLINE_URL);
    if (offlineResponse) {
      return offlineResponse;
    }
  }
  
  // 返回基本的离线响应
  return new Response('离线状态，请检查网络连接', {
    status: 503,
    statusText: 'Service Unavailable',
    headers: {
      'Content-Type': 'text/plain; charset=utf-8'
    }
  });
}

// 判断是否为静态资源
function isStaticResource(url) {
  const staticExtensions = ['.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.woff', '.woff2'];
  const pathname = url.pathname.toLowerCase();
  return staticExtensions.some(ext => pathname.endsWith(ext)) || 
         url.hostname.includes('cdn.') || 
         url.hostname.includes('jsdelivr.net') ||
         url.hostname.includes('tailwindcss.com');
}

// 后台同步 - 用于离线时的数据同步
self.addEventListener('sync', event => {
  console.log('Background sync triggered:', event.tag);
  
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync());
  }
});

async function doBackgroundSync() {
  try {
    // 这里可以实现后台数据同步逻辑
    console.log('Performing background sync...');
    
    // 向所有客户端发送同步完成消息
    const clients = await self.clients.matchAll();
    clients.forEach(client => {
      client.postMessage({
        type: 'BACKGROUND_SYNC_COMPLETE',
        data: { timestamp: Date.now() }
      });
    });
  } catch (error) {
    console.error('Background sync failed:', error);
  }
}

// 推送通知
self.addEventListener('push', event => {
  console.log('Push notification received:', event);
  
  const options = {
    body: event.data ? event.data.text() : '有新的更新可用',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/icon-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: '查看更新',
        icon: '/icons/icon-96x96.png'
      },
      {
        action: 'close',
        title: '关闭',
        icon: '/icons/icon-96x96.png'
      }
    ]
  };
  
  event.waitUntil(
    self.registration.showNotification('免费视频资源导航', options)
  );
});

// 通知点击事件
self.addEventListener('notificationclick', event => {
  console.log('Notification click received:', event);
  
  event.notification.close();
  
  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});

// 消息处理 - 与主线程通信
self.addEventListener('message', event => {
  console.log('Message received in SW:', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'GET_VERSION') {
    event.ports[0].postMessage({ version: CACHE_NAME });
  }
});
