<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>创建基本图标</title>
</head>
<body>
    <script>
        // 创建基本的Canvas图标
        function createIcon(size) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = size;
            canvas.height = size;

            // 背景渐变
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#165DFF');
            gradient.addColorStop(1, '#0E4FE1');
            
            // 绘制圆角矩形背景
            const radius = size * 0.15;
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, radius);
            ctx.fill();

            // 绘制播放按钮
            const centerX = size / 2;
            const centerY = size / 2;
            const playRadius = size * 0.18;

            // 外圈
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
            ctx.lineWidth = size * 0.015;
            ctx.beginPath();
            ctx.arc(centerX, centerY, playRadius * 1.3, 0, 2 * Math.PI);
            ctx.stroke();

            // 播放按钮圆形背景
            ctx.fillStyle = 'white';
            ctx.beginPath();
            ctx.arc(centerX, centerY, playRadius, 0, 2 * Math.PI);
            ctx.fill();

            // 播放三角形
            ctx.fillStyle = '#165DFF';
            ctx.beginPath();
            const triangleSize = playRadius * 0.6;
            ctx.moveTo(centerX - triangleSize * 0.3, centerY - triangleSize * 0.6);
            ctx.lineTo(centerX - triangleSize * 0.3, centerY + triangleSize * 0.6);
            ctx.lineTo(centerX + triangleSize * 0.7, centerY);
            ctx.closePath();
            ctx.fill();

            return canvas;
        }

        // 下载图标
        function downloadIcon(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 生成所有尺寸的图标
        const sizes = [72, 96, 128, 144, 152, 192, 384, 512];
        
        sizes.forEach(size => {
            const canvas = createIcon(size);
            downloadIcon(canvas, `icon-${size}x${size}.png`);
        });

        // 显示完成消息
        setTimeout(() => {
            document.body.innerHTML = `
                <div style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                    <h2>图标生成完成！</h2>
                    <p>请将下载的图标文件放入 icons/ 目录中</p>
                    <p>生成的图标尺寸：${sizes.join('x, ')}x</p>
                </div>
            `;
        }, 1000);
    </script>
</body>
</html>
