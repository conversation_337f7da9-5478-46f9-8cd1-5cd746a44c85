<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图标生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon-preview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .icon-item {
            text-align: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .icon-item canvas {
            border: 1px solid #eee;
            border-radius: 4px;
        }
        .download-btn {
            background: #165DFF;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 5px;
        }
        .download-btn:hover {
            background: #0E4FE1;
        }
        .generate-all {
            background: #10B981;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 20px 0;
        }
        .generate-all:hover {
            background: #059669;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PWA 图标生成器</h1>
        <p>基于SVG图标生成不同尺寸的PNG文件</p>
        
        <button class="generate-all" onclick="generateAllIcons()">生成所有图标</button>
        
        <div class="icon-preview" id="iconPreview">
            <!-- 图标预览将在这里生成 -->
        </div>
    </div>

    <script>
        // 需要生成的图标尺寸
        const iconSizes = [72, 96, 128, 144, 152, 192, 384, 512];
        
        // SVG内容
        const svgContent = `
        <svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" style="stop-color:#165DFF;stop-opacity:1" />
              <stop offset="100%" style="stop-color:#0E4FE1;stop-opacity:1" />
            </linearGradient>
            <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
              <stop offset="100%" style="stop-color:#f0f9ff;stop-opacity:1" />
            </linearGradient>
          </defs>
          
          <rect width="512" height="512" rx="80" ry="80" fill="url(#bgGradient)"/>
          <circle cx="256" cy="256" r="120" fill="none" stroke="url(#iconGradient)" stroke-width="8" opacity="0.3"/>
          <circle cx="256" cy="256" r="90" fill="url(#iconGradient)"/>
          <polygon points="230,220 230,292 310,256" fill="#165DFF"/>
          
          <g opacity="0.6">
            <path d="M 180 180 Q 256 120 332 180" fill="none" stroke="url(#iconGradient)" stroke-width="4" stroke-linecap="round"/>
            <path d="M 180 332 Q 256 392 332 332" fill="none" stroke="url(#iconGradient)" stroke-width="4" stroke-linecap="round"/>
            <path d="M 150 150 Q 256 80 362 150" fill="none" stroke="url(#iconGradient)" stroke-width="3" stroke-linecap="round" opacity="0.7"/>
            <path d="M 150 362 Q 256 432 362 362" fill="none" stroke="url(#iconGradient)" stroke-width="3" stroke-linecap="round" opacity="0.7"/>
          </g>
          
          <circle cx="140" cy="140" r="6" fill="url(#iconGradient)" opacity="0.8"/>
          <circle cx="372" cy="140" r="6" fill="url(#iconGradient)" opacity="0.8"/>
          <circle cx="140" cy="372" r="6" fill="url(#iconGradient)" opacity="0.8"/>
          <circle cx="372" cy="372" r="6" fill="url(#iconGradient)" opacity="0.8"/>
        </svg>`;

        // 将SVG转换为Canvas
        function svgToCanvas(svgString, size) {
            return new Promise((resolve) => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = size;
                canvas.height = size;

                const img = new Image();
                img.onload = function() {
                    ctx.drawImage(img, 0, 0, size, size);
                    resolve(canvas);
                };

                const blob = new Blob([svgString], { type: 'image/svg+xml' });
                const url = URL.createObjectURL(blob);
                img.src = url;
            });
        }

        // 下载Canvas为PNG
        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }

        // 生成单个图标
        async function generateIcon(size) {
            const canvas = await svgToCanvas(svgContent, size);
            return canvas;
        }

        // 生成所有图标
        async function generateAllIcons() {
            const preview = document.getElementById('iconPreview');
            preview.innerHTML = '';

            for (const size of iconSizes) {
                const canvas = await generateIcon(size);
                
                const item = document.createElement('div');
                item.className = 'icon-item';
                
                const displayCanvas = canvas.cloneNode(true);
                displayCanvas.style.width = '80px';
                displayCanvas.style.height = '80px';
                
                const downloadBtn = document.createElement('button');
                downloadBtn.className = 'download-btn';
                downloadBtn.textContent = `下载 ${size}x${size}`;
                downloadBtn.onclick = () => downloadCanvas(canvas, `icon-${size}x${size}.png`);
                
                item.innerHTML = `
                    <div>${size}x${size}</div>
                    <div style="margin: 10px 0;"></div>
                `;
                item.children[1].appendChild(displayCanvas);
                item.appendChild(downloadBtn);
                
                preview.appendChild(item);
            }
        }

        // 页面加载完成后自动生成预览
        document.addEventListener('DOMContentLoaded', generateAllIcons);
    </script>
</body>
</html>
