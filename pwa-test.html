<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA 功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-card {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 5px 10px;
            border-radius: 5px;
            font-weight: bold;
            margin-left: 10px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        button {
            background: #165DFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0E4FE1; }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <h1>PWA 功能测试页面</h1>
    <p>这个页面用于测试免费视频资源导航的PWA功能是否正常工作。</p>

    <div class="test-card">
        <h3>1. Service Worker 状态</h3>
        <p>检查 Service Worker 是否正确注册和激活</p>
        <div id="sw-status">检查中...</div>
        <button onclick="testServiceWorker()">重新检查</button>
    </div>

    <div class="test-card">
        <h3>2. 缓存功能</h3>
        <p>检查应用资源是否正确缓存</p>
        <div id="cache-status">检查中...</div>
        <button onclick="testCache()">检查缓存</button>
    </div>

    <div class="test-card">
        <h3>3. 离线功能</h3>
        <p>测试应用在离线状态下的表现</p>
        <div id="offline-status">检查中...</div>
        <button onclick="testOffline()">模拟离线</button>
    </div>

    <div class="test-card">
        <h3>4. 安装提示</h3>
        <p>检查PWA安装提示功能</p>
        <div id="install-status">检查中...</div>
        <button onclick="testInstall()">触发安装提示</button>
    </div>

    <div class="test-card">
        <h3>5. Manifest 文件</h3>
        <p>验证Web App Manifest配置</p>
        <div id="manifest-status">检查中...</div>
        <button onclick="testManifest()">检查Manifest</button>
    </div>

    <div class="test-card">
        <h3>6. 网络状态</h3>
        <p>当前网络连接状态</p>
        <div id="network-status">检查中...</div>
        <button onclick="testNetwork()">刷新状态</button>
    </div>

    <div class="test-card">
        <h3>测试结果汇总</h3>
        <div id="summary">等待测试完成...</div>
        <button onclick="runAllTests()">运行所有测试</button>
    </div>

    <script>
        // 测试结果存储
        const testResults = {};

        // 初始化测试
        document.addEventListener('DOMContentLoaded', () => {
            runAllTests();
        });

        // Service Worker 测试
        async function testServiceWorker() {
            const statusEl = document.getElementById('sw-status');
            
            if ('serviceWorker' in navigator) {
                try {
                    const registration = await navigator.serviceWorker.getRegistration();
                    if (registration) {
                        if (registration.active) {
                            statusEl.innerHTML = '<span class="status success">✅ Service Worker 已激活</span>';
                            testResults.serviceWorker = true;
                        } else {
                            statusEl.innerHTML = '<span class="status warning">⚠️ Service Worker 已注册但未激活</span>';
                            testResults.serviceWorker = false;
                        }
                    } else {
                        statusEl.innerHTML = '<span class="status error">❌ Service Worker 未注册</span>';
                        testResults.serviceWorker = false;
                    }
                } catch (error) {
                    statusEl.innerHTML = `<span class="status error">❌ 错误: ${error.message}</span>`;
                    testResults.serviceWorker = false;
                }
            } else {
                statusEl.innerHTML = '<span class="status error">❌ 浏览器不支持 Service Worker</span>';
                testResults.serviceWorker = false;
            }
            updateSummary();
        }

        // 缓存测试
        async function testCache() {
            const statusEl = document.getElementById('cache-status');
            
            if ('caches' in window) {
                try {
                    const cacheNames = await caches.keys();
                    const videoCaches = cacheNames.filter(name => name.includes('video-nav'));
                    
                    if (videoCaches.length > 0) {
                        const cache = await caches.open(videoCaches[0]);
                        const cachedRequests = await cache.keys();
                        statusEl.innerHTML = `<span class="status success">✅ 缓存正常 (${cachedRequests.length} 个资源)</span>`;
                        testResults.cache = true;
                    } else {
                        statusEl.innerHTML = '<span class="status warning">⚠️ 未找到应用缓存</span>';
                        testResults.cache = false;
                    }
                } catch (error) {
                    statusEl.innerHTML = `<span class="status error">❌ 缓存错误: ${error.message}</span>`;
                    testResults.cache = false;
                }
            } else {
                statusEl.innerHTML = '<span class="status error">❌ 浏览器不支持缓存API</span>';
                testResults.cache = false;
            }
            updateSummary();
        }

        // 离线测试
        function testOffline() {
            const statusEl = document.getElementById('offline-status');
            
            if (navigator.onLine) {
                statusEl.innerHTML = '<span class="status success">✅ 当前在线</span><div class="test-result">提示：要测试离线功能，请断开网络连接后刷新页面</div>';
                testResults.offline = true;
            } else {
                statusEl.innerHTML = '<span class="status warning">⚠️ 当前离线</span><div class="test-result">离线模式下应该能看到缓存的内容</div>';
                testResults.offline = true;
            }
            updateSummary();
        }

        // 安装测试
        function testInstall() {
            const statusEl = document.getElementById('install-status');
            
            // 检查是否已安装
            if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
                statusEl.innerHTML = '<span class="status success">✅ 应用已安装</span>';
                testResults.install = true;
            } else if ('BeforeInstallPromptEvent' in window) {
                statusEl.innerHTML = '<span class="status success">✅ 支持安装提示</span><div class="test-result">在主应用中会显示安装提示</div>';
                testResults.install = true;
            } else {
                statusEl.innerHTML = '<span class="status warning">⚠️ 浏览器可能不支持PWA安装</span>';
                testResults.install = false;
            }
            updateSummary();
        }

        // Manifest 测试
        async function testManifest() {
            const statusEl = document.getElementById('manifest-status');
            
            try {
                const response = await fetch('/manifest.json');
                if (response.ok) {
                    const manifest = await response.json();
                    statusEl.innerHTML = `<span class="status success">✅ Manifest 正常</span><div class="test-result">应用名称: ${manifest.name}</div>`;
                    testResults.manifest = true;
                } else {
                    statusEl.innerHTML = '<span class="status error">❌ Manifest 文件无法访问</span>';
                    testResults.manifest = false;
                }
            } catch (error) {
                statusEl.innerHTML = `<span class="status error">❌ Manifest 错误: ${error.message}</span>`;
                testResults.manifest = false;
            }
            updateSummary();
        }

        // 网络状态测试
        function testNetwork() {
            const statusEl = document.getElementById('network-status');
            
            const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
            let networkInfo = navigator.onLine ? '在线' : '离线';
            
            if (connection) {
                networkInfo += ` (${connection.effectiveType || 'unknown'})`;
            }
            
            statusEl.innerHTML = `<span class="status ${navigator.onLine ? 'success' : 'warning'}">${navigator.onLine ? '✅' : '⚠️'} ${networkInfo}</span>`;
            testResults.network = navigator.onLine;
            updateSummary();
        }

        // 运行所有测试
        async function runAllTests() {
            await testServiceWorker();
            await testCache();
            testOffline();
            testInstall();
            await testManifest();
            testNetwork();
        }

        // 更新汇总
        function updateSummary() {
            const summaryEl = document.getElementById('summary');
            const total = Object.keys(testResults).length;
            const passed = Object.values(testResults).filter(Boolean).length;
            
            if (total === 0) {
                summaryEl.innerHTML = '等待测试完成...';
                return;
            }
            
            const percentage = Math.round((passed / total) * 100);
            let statusClass = 'success';
            if (percentage < 70) statusClass = 'error';
            else if (percentage < 90) statusClass = 'warning';
            
            summaryEl.innerHTML = `
                <span class="status ${statusClass}">
                    ${percentage}% 测试通过 (${passed}/${total})
                </span>
                <div class="test-result">
                    ${percentage >= 90 ? '🎉 PWA功能运行良好！' : 
                      percentage >= 70 ? '⚠️ 部分功能需要检查' : 
                      '❌ 需要修复多个问题'}
                </div>
            `;
        }

        // 监听网络状态变化
        window.addEventListener('online', testNetwork);
        window.addEventListener('offline', testNetwork);
    </script>
</body>
</html>
