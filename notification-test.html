<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通知系统测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-2xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center">通知系统测试</h1>
        
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">测试不同类型的通知</h2>
            <div class="grid grid-cols-2 gap-4">
                <button onclick="showNotification('操作成功完成！', 'success')" 
                        class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    成功通知
                </button>
                <button onclick="showNotification('发生了一个错误', 'error')" 
                        class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                    错误通知
                </button>
                <button onclick="showNotification('这是一个警告信息', 'warning')" 
                        class="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600">
                    警告通知
                </button>
                <button onclick="showNotification('这是一条信息', 'info')" 
                        class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    信息通知
                </button>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">测试多个通知</h2>
            <div class="space-x-4">
                <button onclick="showMultipleNotifications()" 
                        class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                    显示多个通知
                </button>
                <button onclick="clearAllNotifications()" 
                        class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                    清除所有通知
                </button>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-semibold mb-4">测试长时间通知</h2>
            <button onclick="showNotification('这是一个长时间显示的通知，将显示10秒', 'info', 10000)" 
                    class="bg-indigo-500 text-white px-4 py-2 rounded hover:bg-indigo-600">
                长时间通知 (10秒)
            </button>
        </div>
    </div>

    <!-- 通知提示容器 -->
    <div id="notification-container" class="fixed bottom-6 right-6 z-50 space-y-3 pointer-events-none">
        <!-- 通知将动态添加到这里 -->
    </div>

    <script>
        // 通知计数器，用于生成唯一ID
        let notificationCounter = 0;
        const notificationContainer = document.getElementById('notification-container');

        // 显示通知
        function showNotification(message, type = 'success', duration = 3000) {
            const notificationId = `notification-${++notificationCounter}`;
            
            // 创建通知元素
            const notification = document.createElement('div');
            notification.id = notificationId;
            notification.className = `
                bg-white text-gray-800 px-4 py-3 rounded-lg shadow-lg 
                transform translate-x-full opacity-0 transition-all duration-300 
                flex items-center min-w-64 max-w-80 pointer-events-auto
                border-l-4 ${getNotificationStyle(type)}
            `.trim().replace(/\s+/g, ' ');
            
            // 设置图标
            const icon = getNotificationIcon(type);
            
            // 设置内容
            notification.innerHTML = `
                <i class="fa ${icon} mr-3 text-lg"></i>
                <span class="flex-1 text-sm font-medium">${message}</span>
                <button onclick="removeNotification('${notificationId}')" 
                        class="ml-2 text-gray-400 hover:text-gray-600 transition-colors">
                    <i class="fa fa-times"></i>
                </button>
            `;
            
            // 添加到容器
            notificationContainer.appendChild(notification);
            
            // 触发进入动画
            setTimeout(() => {
                notification.classList.remove('translate-x-full', 'opacity-0');
                notification.classList.add('translate-x-0', 'opacity-100');
            }, 10);
            
            // 自动移除
            setTimeout(() => {
                removeNotification(notificationId);
            }, duration);
            
            return notificationId;
        }

        // 获取通知样式
        function getNotificationStyle(type) {
            switch (type) {
                case 'success':
                    return 'border-green-500';
                case 'error':
                    return 'border-red-500';
                case 'warning':
                    return 'border-yellow-500';
                case 'info':
                    return 'border-blue-500';
                default:
                    return 'border-green-500';
            }
        }

        // 获取通知图标
        function getNotificationIcon(type) {
            switch (type) {
                case 'success':
                    return 'fa-check-circle text-green-500';
                case 'error':
                    return 'fa-exclamation-circle text-red-500';
                case 'warning':
                    return 'fa-exclamation-triangle text-yellow-500';
                case 'info':
                    return 'fa-info-circle text-blue-500';
                default:
                    return 'fa-check-circle text-green-500';
            }
        }

        // 移除通知
        function removeNotification(notificationId) {
            const notification = document.getElementById(notificationId);
            if (notification) {
                notification.classList.remove('translate-x-0', 'opacity-100');
                notification.classList.add('translate-x-full', 'opacity-0');
                
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }
        }

        // 清除所有通知
        function clearAllNotifications() {
            const notifications = notificationContainer.querySelectorAll('[id^="notification-"]');
            notifications.forEach(notification => {
                removeNotification(notification.id);
            });
        }

        // 显示多个通知
        function showMultipleNotifications() {
            showNotification('第一个通知', 'success');
            setTimeout(() => showNotification('第二个通知', 'info'), 500);
            setTimeout(() => showNotification('第三个通知', 'warning'), 1000);
            setTimeout(() => showNotification('第四个通知', 'error'), 1500);
        }
    </script>
</body>
</html>
