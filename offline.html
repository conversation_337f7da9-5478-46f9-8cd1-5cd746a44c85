<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>离线模式 - 免费视频资源导航</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .offline-container {
            text-align: center;
            max-width: 500px;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .offline-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.8;
        }
        
        .offline-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        
        .offline-message {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .retry-button {
            background: #165DFF;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 8px;
        }
        
        .retry-button:hover {
            background: #0E4FE1;
            transform: translateY(-2px);
        }
        
        .offline-tips {
            margin-top: 2rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            text-align: left;
        }
        
        .offline-tips h3 {
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }
        
        .offline-tips ul {
            list-style: none;
            padding-left: 0;
        }
        
        .offline-tips li {
            margin: 0.5rem 0;
            padding-left: 1.5rem;
            position: relative;
        }
        
        .offline-tips li:before {
            content: "•";
            position: absolute;
            left: 0;
            color: #165DFF;
            font-weight: bold;
        }
        
        .connection-status {
            margin-top: 1rem;
            padding: 0.5rem;
            border-radius: 5px;
            font-size: 0.9rem;
        }
        
        .status-online {
            background: rgba(16, 185, 129, 0.2);
            color: #10B981;
        }
        
        .status-offline {
            background: rgba(239, 68, 68, 0.2);
            color: #EF4444;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon pulse">📡</div>
        <h1 class="offline-title">离线模式</h1>
        <p class="offline-message">
            您当前处于离线状态。请检查网络连接后重试。
        </p>
        
        <div class="connection-status" id="connectionStatus">
            <span id="statusText">检查网络连接中...</span>
        </div>
        
        <div style="margin-top: 1.5rem;">
            <button class="retry-button" onclick="retryConnection()">
                🔄 重新连接
            </button>
            <button class="retry-button" onclick="goHome()">
                🏠 返回首页
            </button>
        </div>
        
        <div class="offline-tips">
            <h3>💡 离线使用提示</h3>
            <ul>
                <li>已缓存的页面可以正常浏览</li>
                <li>网络恢复后会自动同步最新数据</li>
                <li>可以查看之前访问过的内容</li>
                <li>建议连接WiFi以获得最佳体验</li>
            </ul>
        </div>
    </div>

    <script>
        // 检查网络连接状态
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connectionStatus');
            const statusText = document.getElementById('statusText');
            
            if (navigator.onLine) {
                statusElement.className = 'connection-status status-online';
                statusText.textContent = '✅ 网络已连接';
            } else {
                statusElement.className = 'connection-status status-offline';
                statusText.textContent = '❌ 网络未连接';
            }
        }
        
        // 重试连接
        function retryConnection() {
            if (navigator.onLine) {
                window.location.reload();
            } else {
                alert('请先检查网络连接');
            }
        }
        
        // 返回首页
        function goHome() {
            window.location.href = '/';
        }
        
        // 监听网络状态变化
        window.addEventListener('online', () => {
            updateConnectionStatus();
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        });
        
        window.addEventListener('offline', updateConnectionStatus);
        
        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            updateConnectionStatus();
            
            // 定期检查网络状态
            setInterval(() => {
                if (navigator.onLine) {
                    // 尝试发送一个简单的请求来验证网络
                    fetch('/', { method: 'HEAD', cache: 'no-cache' })
                        .then(() => {
                            window.location.reload();
                        })
                        .catch(() => {
                            // 网络仍然不可用
                        });
                }
            }, 5000);
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
                e.preventDefault();
                retryConnection();
            }
            if (e.key === 'Escape') {
                goHome();
            }
        });
    </script>
</body>
</html>
